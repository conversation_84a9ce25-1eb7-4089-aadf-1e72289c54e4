from odoo import models, api

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.onchange('partner_shipping_id')
    def _onchange_partner_shipping_id_lb_rmks_flag(self):
        if self.partner_shipping_id:
            flag = self.partner_shipping_id.lb_rmks_flag
            prefix = ''
            if flag == 'zheng':
                prefix = '正；'
            elif flag == 'fan':
                prefix = '反；'
            # 当 flag 为空('')时，不做处理，符合需求要求

            if prefix:  # 只有当有有效的前缀时才处理
                # 操作订单级别的 LB Rmks 字段 (x_studio_so_remarks_1)
                if hasattr(self, 'x_studio_so_remarks_1'):
                    current_value = self.x_studio_so_remarks_1 or ''
                    # 检查是否已经有前缀，避免重复添加
                    if not current_value.startswith(('正；', '反；')):
                        self.x_studio_so_remarks_1 = f'{prefix}{current_value}'
        # 若未设置标记或为空，不做处理